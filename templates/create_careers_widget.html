{% extends 'main.html' %}
{% load static %}
{% load i18n %}

{% block content %}
{% csrf_token %}
<div class="careers-builder-container">
    <div class="container-fluid h-100">
        <div class="row h-100">
            <!-- Left Panel - Customization Controls -->
            <div class="col-lg-4 col-xl-3 builder-sidebar">
                <div class="sidebar-header">
                    <h4><i class="fas fa-puzzle-piece me-2"></i>{% trans "Widget Builder" %}</h4>
                    <p class="text-muted">{% trans "Customize your careers widget" %}</p>
                </div>

                <div class="customization-panels">
                    <!-- Company Branding Panel -->
                    <div class="panel-section">
                        <h5 class="panel-title">
                            <i class="fas fa-building me-2"></i>
                            {% trans "Company Branding" %}
                        </h5>
                        
                        <div class="form-group mb-3">
                            <label for="companyName" class="form-label">{% trans "Company Name" %}</label>
                            <input type="text" class="form-control" id="companyName" placeholder="{% trans 'Enter company name' %}" value="{{ employer.employer_name|default:'Your Company' }}">
                        </div>

                        <div class="form-group mb-3">
                            <label for="companyTagline" class="form-label">{% trans "Tagline" %}</label>
                            <input type="text" class="form-control" id="companyTagline" placeholder="{% trans 'Enter company tagline' %}" value="Join our amazing team">
                        </div>

                        <div class="form-group mb-3">
                            <label for="logoUpload" class="form-label">{% trans "Company Logo" %}</label>
                            <input type="file" class="form-control" id="logoUpload" accept="image/*">
                            <small class="form-text text-muted">{% trans "Recommended: 200x80px, PNG or JPG" %}</small>
                        </div>
                    </div>

                    <!-- Design Customization Panel -->
                    <div class="panel-section">
                        <h5 class="panel-title">
                            <i class="fas fa-palette me-2"></i>
                            {% trans "Design & Colors" %}
                        </h5>
                        
                        <div class="form-group mb-3">
                            <label for="primaryColor" class="form-label">{% trans "Primary Color" %}</label>
                            <input type="color" class="form-control form-control-color" id="primaryColor" value="#343a40">
                        </div>

                        <div class="form-group mb-3">
                            <label for="backgroundColor" class="form-label">{% trans "Background Color" %}</label>
                            <input type="color" class="form-control form-control-color" id="backgroundColor" value="#ffffff">
                        </div>

                        <div class="form-group mb-3">
                            <label for="textColor" class="form-label">{% trans "Text Color" %}</label>
                            <input type="color" class="form-control form-control-color" id="textColor" value="#333333">
                        </div>

                        <div class="form-group mb-3">
                            <label for="widgetStyle" class="form-label">{% trans "Widget Style" %}</label>
                            <select class="form-select" id="widgetStyle">
                                <option value="modern">{% trans "Modern" %}</option>
                                <option value="classic">{% trans "Classic" %}</option>
                                <option value="minimal">{% trans "Minimal" %}</option>
                            </select>
                        </div>
                    </div>

                    <!-- Content Settings Panel -->
                    <div class="panel-section">
                        <h5 class="panel-title">
                            <i class="fas fa-cog me-2"></i>
                            {% trans "Content Settings" %}
                        </h5>
                        
                        <div class="form-group mb-3">
                            <label for="maxJobs" class="form-label">{% trans "Max Jobs to Display" %}</label>
                            <select class="form-select" id="maxJobs">
                                <option value="3">3 {% trans "jobs" %}</option>
                                <option value="5" selected>5 {% trans "jobs" %}</option>
                                <option value="10">10 {% trans "jobs" %}</option>
                                <option value="all">{% trans "All jobs" %}</option>
                            </select>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="showSalary" checked>
                            <label class="form-check-label" for="showSalary">
                                {% trans "Show Salary Information" %}
                            </label>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="showLocation" checked>
                            <label class="form-check-label" for="showLocation">
                                {% trans "Show Job Location" %}
                            </label>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="showDate" checked>
                            <label class="form-check-label" for="showDate">
                                {% trans "Show Posted Date" %}
                            </label>
                        </div>
                    </div>

                    <!-- Generate Widget Button -->
                    <div class="panel-section">
                        <button class="btn btn-primary btn-lg w-100" onclick="generateWidget()">
                            <i class="fas fa-code me-2"></i>
                            {% trans "Generate Widget Code" %}
                        </button>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Live Preview -->
            <div class="col-lg-8 col-xl-9 preview-panel">
                <div class="preview-header">
                    <h4><i class="fas fa-eye me-2"></i>{% trans "Live Preview" %}</h4>
                    <div class="preview-controls">
                        <button class="btn btn-outline-secondary btn-sm" onclick="togglePreviewMode('desktop')">
                            <i class="fas fa-desktop"></i>
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="togglePreviewMode('tablet')">
                            <i class="fas fa-tablet-alt"></i>
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="togglePreviewMode('mobile')">
                            <i class="fas fa-mobile-alt"></i>
                        </button>
                    </div>
                </div>

                <div class="preview-container" id="previewContainer">
                    <div class="preview-frame" id="previewFrame">
                        <!-- Widget preview will be rendered here -->
                        <div id="widgetPreview" class="widget-preview">
                            <!-- This will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Widget Code Modal -->
<div class="modal fade" id="widgetCodeModal" tabindex="-1" aria-labelledby="widgetCodeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="widgetCodeModalLabel">
                    <i class="fas fa-code me-2"></i>
                    {% trans "Your Widget Code" %}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% trans "Copy this code and paste it into your website where you want the careers widget to appear." %}
                </div>
                
                <div class="code-container">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <label for="widgetCode" class="form-label mb-0">{% trans "HTML Widget Code" %}</label>
                        <button class="btn btn-outline-secondary btn-sm" onclick="copyWidgetCode()">
                            <i class="fas fa-copy me-1"></i>
                            {% trans "Copy Code" %}
                        </button>
                    </div>
                    <textarea class="form-control code-textarea" id="widgetCode" rows="15" readonly></textarea>
                </div>

                <div class="mt-4">
                    <h6>{% trans "Integration Instructions:" %}</h6>
                    <ol class="small text-muted">
                        <li>{% trans "Copy the HTML code above" %}</li>
                        <li>{% trans "Paste it into your website's HTML where you want the widget to appear" %}</li>
                        <li>{% trans "The widget will automatically load your latest job postings" %}</li>
                        <li>{% trans "The widget is responsive and will adapt to your website's layout" %}</li>
                    </ol>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
                <button type="button" class="btn btn-primary" onclick="downloadWidget()">
                    <i class="fas fa-download me-2"></i>
                    {% trans "Download as HTML File" %}
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.careers-builder-container {
    height: 100vh;
    overflow: hidden;
}

.builder-sidebar {
    background: #f8f9fa;
    border-right: 1px solid #dee2e6;
    height: 100vh;
    overflow-y: auto;
    padding: 1.5rem;
}

.sidebar-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.panel-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.panel-title {
    color: #343a40;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.preview-panel {
    background: #ffffff;
    height: 100vh;
    overflow: hidden;
    padding: 1.5rem;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.preview-controls .btn {
    margin-left: 0.5rem;
}

.preview-container {
    height: calc(100vh - 120px);
    background: #f8f9fa;
    border-radius: 8px;
    padding: 2rem;
    overflow: auto;
}

.preview-frame {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    min-height: 400px;
    transition: all 0.3s ease;
}

.preview-frame.mobile {
    max-width: 375px;
    margin: 0 auto;
}

.preview-frame.tablet {
    max-width: 768px;
    margin: 0 auto;
}

.widget-preview {
    padding: 2rem;
}

.code-textarea {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
}

.form-control-color {
    width: 100%;
    height: 38px;
}

@media (max-width: 992px) {
    .careers-builder-container {
        height: auto;
    }
    
    .builder-sidebar,
    .preview-panel {
        height: auto;
    }
    
    .preview-container {
        height: 500px;
    }
}
</style>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script>
// Widget builder functionality
let widgetConfig = {
    companyName: '{{ employer.employer_name|default:"Your Company"|escapejs }}',
    tagline: 'Join our amazing team',
    logo: {% if employer.employer_logo_url %}'{{ employer.employer_logo_url|escapejs }}'{% else %}null{% endif %},
    primaryColor: '#343a40',
    backgroundColor: '#ffffff',
    textColor: '#333333',
    style: 'modern',
    maxJobs: 5,
    showSalary: true,
    showLocation: true,
    showDate: true,
    realJobs: [
        {% for vacancy in active_vacancies %}
        {
            title: '{{ vacancy.vacancy_title|escapejs }}',
            location: '{{ vacancy.vacancy_city|escapejs }}, {{ vacancy.vacancy_country|escapejs }}',
            schedule: '{{ vacancy.office_schedule|escapejs }}',
            workType: '{{ vacancy.work_schedule|escapejs }}',
            salaryMin: {{ vacancy.salary_min|default:0 }},
            salaryMax: {{ vacancy.salary_max|default:0 }},
            currency: '{{ vacancy.salary_currency|default:"USD"|escapejs }}',
            description: '{{ vacancy.vacancy_job_description|truncatechars:100|escapejs }}',
            datePosted: '{{ vacancy.vacancy_creation_date|date:"M d, Y"|escapejs }}'
        }{% if not forloop.last %},{% endif %}
        {% endfor %}
    ]
};

// Initialize the builder
document.addEventListener('DOMContentLoaded', function() {
    initializeBuilder();
    loadInitialWidgetData();
    updatePreview();
});

function loadInitialWidgetData() {
    // Set form values from widgetConfig
    document.getElementById('companyName').value = widgetConfig.companyName;
    document.getElementById('companyTagline').value = widgetConfig.tagline;
    document.getElementById('primaryColor').value = widgetConfig.primaryColor;
    document.getElementById('backgroundColor').value = widgetConfig.backgroundColor;
    document.getElementById('textColor').value = widgetConfig.textColor;
    document.getElementById('widgetStyle').value = widgetConfig.style;
    document.getElementById('maxJobs').value = widgetConfig.maxJobs;
    document.getElementById('showSalary').checked = widgetConfig.showSalary;
    document.getElementById('showLocation').checked = widgetConfig.showLocation;
    document.getElementById('showDate').checked = widgetConfig.showDate;
}

function initializeBuilder() {
    // Add event listeners for all form controls
    document.getElementById('companyName').addEventListener('input', updateConfig);
    document.getElementById('companyTagline').addEventListener('input', updateConfig);
    document.getElementById('logoUpload').addEventListener('change', handleLogoUpload);
    document.getElementById('primaryColor').addEventListener('change', updateConfig);
    document.getElementById('backgroundColor').addEventListener('change', updateConfig);
    document.getElementById('textColor').addEventListener('change', updateConfig);
    document.getElementById('widgetStyle').addEventListener('change', updateConfig);
    document.getElementById('maxJobs').addEventListener('change', updateConfig);
    document.getElementById('showSalary').addEventListener('change', updateConfig);
    document.getElementById('showLocation').addEventListener('change', updateConfig);
    document.getElementById('showDate').addEventListener('change', updateConfig);
}

function updateConfig() {
    widgetConfig.companyName = document.getElementById('companyName').value;
    widgetConfig.tagline = document.getElementById('companyTagline').value;
    widgetConfig.primaryColor = document.getElementById('primaryColor').value;
    widgetConfig.backgroundColor = document.getElementById('backgroundColor').value;
    widgetConfig.textColor = document.getElementById('textColor').value;
    widgetConfig.style = document.getElementById('widgetStyle').value;
    widgetConfig.maxJobs = document.getElementById('maxJobs').value;
    widgetConfig.showSalary = document.getElementById('showSalary').checked;
    widgetConfig.showLocation = document.getElementById('showLocation').checked;
    widgetConfig.showDate = document.getElementById('showDate').checked;
    
    updatePreview();
}

function handleLogoUpload(event) {
    const file = event.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            widgetConfig.logo = e.target.result;
            updatePreview();
        };
        reader.readAsDataURL(file);
    }
}

function updatePreview() {
    // This function will render the widget preview
    // Implementation will be added in the next part
    renderWidgetPreview();
}

function generateWidgetJobListings() {
    const jobsToShow = widgetConfig.realJobs && widgetConfig.realJobs.length > 0 ?
        widgetConfig.realJobs.slice(0, widgetConfig.maxJobs) : [];

    if (jobsToShow.length === 0) {
        return `
            <div style="padding: 20px; text-align: center; color: #666;">
                <p>No active job openings at the moment.</p>
                <p style="font-size: 0.9rem;">Check back soon for new opportunities!</p>
            </div>
        `;
    }

    return jobsToShow.map(job => {
        const salaryText = job.salaryMin && job.salaryMax && widgetConfig.showSalary ?
            `<div style="font-size: 0.85rem; color: ${widgetConfig.primaryColor}; margin: 5px 0;">💰 ${job.currency} ${job.salaryMin.toLocaleString()} - ${job.currency} ${job.salaryMax.toLocaleString()}</div>` : '';

        const locationText = widgetConfig.showLocation ?
            `<div style="font-size: 0.85rem; color: #666; margin: 5px 0;">📍 ${job.location}</div>` : '';

        const dateText = widgetConfig.showDate ?
            `<div style="font-size: 0.8rem; color: #999; margin: 5px 0;">📅 ${job.datePosted}</div>` : '';

        return `
            <div style="border: 1px solid #e0e0e0; border-radius: 6px; padding: 15px; margin-bottom: 10px; background: white;">
                <h5 style="color: ${widgetConfig.primaryColor}; margin: 0 0 8px 0; font-size: 1rem;">${job.title}</h5>
                ${locationText}
                ${salaryText}
                <div style="font-size: 0.85rem; color: #666; margin: 5px 0;">🏢 ${job.workType} • 🏠 ${job.schedule}</div>
                ${dateText}
                <div style="margin-top: 10px;">
                    <button style="background: ${widgetConfig.primaryColor}; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-size: 0.85rem; cursor: pointer;">Apply Now</button>
                </div>
            </div>
        `;
    }).join('');
}

function renderWidgetPreview() {
    const previewContainer = document.getElementById('widgetPreview');

    previewContainer.innerHTML = `
        <div style="background: ${widgetConfig.backgroundColor}; color: ${widgetConfig.textColor}; padding: 20px; border-radius: 8px; font-family: Arial, sans-serif; max-width: 400px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 20px;">
                ${widgetConfig.logo ? `<img src="${widgetConfig.logo}" alt="Logo" style="max-height: 60px; margin-bottom: 10px;">` : ''}
                <h3 style="color: ${widgetConfig.primaryColor}; margin: 0; font-size: 1.2rem;">${widgetConfig.companyName}</h3>
                <p style="margin: 5px 0 0 0; opacity: 0.8; font-size: 0.9rem;">${widgetConfig.tagline}</p>
            </div>
            <div style="border-top: 1px solid #eee; padding-top: 20px;">
                <h4 style="color: ${widgetConfig.primaryColor}; margin-bottom: 15px; font-size: 1.1rem;">Current Opportunities</h4>
                ${generateWidgetJobListings()}
            </div>
        </div>
    `;
                <div style="space-y: 10px;">
                    <!-- Sample job listings will be rendered here -->
                    <div style="border: 1px solid #eee; padding: 15px; border-radius: 6px; margin-bottom: 10px;">
                        <h5 style="margin: 0 0 5px 0; color: ${widgetConfig.primaryColor};">Senior Developer</h5>
                        ${widgetConfig.showLocation ? '<p style="margin: 0; font-size: 0.9em; opacity: 0.7;">📍 Remote</p>' : ''}
                        ${widgetConfig.showSalary ? '<p style="margin: 0; font-size: 0.9em; opacity: 0.7;">💰 $80,000 - $120,000</p>' : ''}
                        ${widgetConfig.showDate ? '<p style="margin: 0; font-size: 0.9em; opacity: 0.7;">📅 Posted 2 days ago</p>' : ''}
                    </div>
                </div>
            </div>
        </div>
    `;
}

function togglePreviewMode(mode) {
    const frame = document.getElementById('previewFrame');
    frame.className = 'preview-frame ' + mode;
    
    // Update button states
    document.querySelectorAll('.preview-controls .btn').forEach(btn => {
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-outline-secondary');
    });
    event.target.classList.remove('btn-outline-secondary');
    event.target.classList.add('btn-primary');
}

function generateWidget() {
    // Generate the widget code and show modal
    const widgetCode = generateWidgetCode();
    document.getElementById('widgetCode').value = widgetCode;
    
    const modal = new bootstrap.Modal(document.getElementById('widgetCodeModal'));
    modal.show();
}

function generateWidgetCode() {
    const companySlug = widgetConfig.companyName.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');

    return `<!-- Workloupe Careers Widget -->
<div id="workloupe-careers-widget-${Date.now()}" style="font-family: Arial, sans-serif; max-width: 400px; margin: 20px auto;"></div>

<script>
(function() {
    // Widget configuration
    const config = {
        companyName: "${widgetConfig.companyName}",
        tagline: "${widgetConfig.tagline}",
        primaryColor: "${widgetConfig.primaryColor}",
        backgroundColor: "${widgetConfig.backgroundColor}",
        textColor: "${widgetConfig.textColor}",
        maxJobs: ${widgetConfig.maxJobs},
        showSalary: ${widgetConfig.showSalary},
        showLocation: ${widgetConfig.showLocation},
        showDate: ${widgetConfig.showDate},
        apiUrl: "https://workloupe.com/api/company/${companySlug}/jobs/",
        companyUrl: "https://workloupe.com/company/${companySlug}"
    };

    const widgetId = 'workloupe-careers-widget-${Date.now()}';
    const container = document.getElementById(widgetId);

    // Widget styles
    const styles = \`
        .workloupe-widget {
            background: \${config.backgroundColor};
            color: \${config.textColor};
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            font-family: Arial, sans-serif;
        }
        .workloupe-widget h3 {
            color: \${config.primaryColor};
            margin: 0 0 10px 0;
            font-size: 1.2rem;
            text-align: center;
        }
        .workloupe-widget .tagline {
            text-align: center;
            opacity: 0.8;
            font-size: 0.9rem;
            margin-bottom: 20px;
        }
        .workloupe-widget .job-item {
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 10px;
            background: white;
        }
        .workloupe-widget .job-title {
            color: \${config.primaryColor};
            margin: 0 0 8px 0;
            font-size: 1rem;
            font-weight: bold;
        }
        .workloupe-widget .job-details {
            font-size: 0.85rem;
            color: #666;
            margin: 5px 0;
        }
        .workloupe-widget .apply-btn {
            background: \${config.primaryColor};
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 0.85rem;
            cursor: pointer;
            margin-top: 10px;
        }
        .workloupe-widget .apply-btn:hover {
            opacity: 0.9;
        }
        .workloupe-widget .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .workloupe-widget .footer {
            text-align: center;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }
        .workloupe-widget .footer a {
            color: \${config.primaryColor};
            text-decoration: none;
            font-size: 0.9rem;
        }
    \`;

    // Add styles to page
    const styleSheet = document.createElement('style');
    styleSheet.textContent = styles;
    document.head.appendChild(styleSheet);

    // Load widget content
    function loadWidget() {
        container.innerHTML = \`
            <div class="workloupe-widget">
                <h3>\${config.companyName}</h3>
                <div class="tagline">\${config.tagline}</div>
                <div class="loading">Loading job opportunities...</div>
            </div>
        \`;

        // Fetch jobs from API
        fetch(config.apiUrl)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.jobs && data.jobs.length > 0) {
                    const jobsToShow = data.jobs.slice(0, config.maxJobs);
                    const jobsHtml = jobsToShow.map(job => {
                        const salaryText = job.salary_min && job.salary_max && config.showSalary ?
                            \`<div class="job-details">💰 \${job.salary_currency || 'USD'} \${job.salary_min.toLocaleString()} - \${job.salary_max.toLocaleString()}</div>\` : '';

                        const locationText = config.showLocation ?
                            \`<div class="job-details">📍 \${job.location}</div>\` : '';

                        const dateText = config.showDate ?
                            \`<div class="job-details">📅 \${job.date_posted}</div>\` : '';

                        return \`
                            <div class="job-item">
                                <div class="job-title">\${job.title}</div>
                                \${locationText}
                                \${salaryText}
                                <div class="job-details">🏢 \${job.work_type} • 🏠 \${job.schedule}</div>
                                \${dateText}
                                <button class="apply-btn" onclick="window.open('\${job.apply_url || config.companyUrl}', '_blank')">Apply Now</button>
                            </div>
                        \`;
                    }).join('');

                    container.innerHTML = \`
                        <div class="workloupe-widget">
                            <h3>\${config.companyName}</h3>
                            <div class="tagline">\${config.tagline}</div>
                            \${jobsHtml}
                            <div class="footer">
                                <a href="\${config.companyUrl}" target="_blank">View All Jobs →</a>
                            </div>
                        </div>
                    \`;
                } else {
                    container.innerHTML = \`
                        <div class="workloupe-widget">
                            <h3>\${config.companyName}</h3>
                            <div class="tagline">\${config.tagline}</div>
                            <div style="text-align: center; padding: 20px; color: #666;">
                                <p>No active job openings at the moment.</p>
                                <p style="font-size: 0.9rem;">Check back soon for new opportunities!</p>
                            </div>
                            <div class="footer">
                                <a href="\${config.companyUrl}" target="_blank">Visit Our Careers Page →</a>
                            </div>
                        </div>
                    \`;
                }
            })
            .catch(error => {
                console.error('Error loading jobs:', error);
                container.innerHTML = \`
                    <div class="workloupe-widget">
                        <h3>\${config.companyName}</h3>
                        <div class="tagline">\${config.tagline}</div>
                        <div style="text-align: center; padding: 20px; color: #666;">
                            <p>Unable to load job listings at the moment.</p>
                            <p style="font-size: 0.9rem;">Please try again later.</p>
                        </div>
                        <div class="footer">
                            <a href="\${config.companyUrl}" target="_blank">Visit Our Careers Page →</a>
                        </div>
                    </div>
                \`;
            });
    }

    // Initialize widget
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', loadWidget);
    } else {
        loadWidget();
    }
})();
</script>`;
}

function copyWidgetCode() {
    const codeTextarea = document.getElementById('widgetCode');
    codeTextarea.select();
    codeTextarea.setSelectionRange(0, 99999);
    
    try {
        document.execCommand('copy');
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check me-1"></i>{% trans "Copied!" %}';
        button.classList.add('btn-success');
        button.classList.remove('btn-outline-secondary');
        
        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 2000);
    } catch (err) {
        alert('{% trans "Failed to copy. Please copy manually." %}');
    }
}

function downloadWidget() {
    const widgetCode = document.getElementById('widgetCode').value;
    const companySlug = widgetConfig.companyName.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');

    if (typeof JSZip !== 'undefined') {
        // Create ZIP package with widget and documentation
        const zip = new JSZip();

        // Add widget HTML file
        zip.file('workloupe-careers-widget.html', widgetCode);

        // Add integration instructions
        const instructions = `# Workloupe Careers Widget Integration Guide

## Quick Start

1. Copy the widget code from workloupe-careers-widget.html
2. Paste it into your website where you want the careers widget to appear
3. The widget will automatically load and display your current job openings

## Integration Options

### Option 1: Direct HTML Integration
Copy the entire content of workloupe-careers-widget.html and paste it into your webpage.

### Option 2: External Script (Recommended)
1. Upload workloupe-careers-widget.html to your web server
2. Include it in your page with:
   <script src="path/to/workloupe-careers-widget.html"></script>

### Option 3: WordPress Integration
1. Go to your WordPress admin panel
2. Navigate to Appearance > Widgets or use the block editor
3. Add a "Custom HTML" widget/block
4. Paste the widget code

### Option 4: Embed in CMS
Most content management systems support custom HTML blocks where you can paste the widget code.

## Customization

The widget automatically fetches live job data from Workloupe. To customize:
- Colors and styling are configured in the Workloupe dashboard
- Widget size and job count can be adjusted in your account settings
- Contact <EMAIL> for advanced customizations

## Troubleshooting

- Ensure your website allows external scripts
- Check that the widget container has enough space (minimum 300px width)
- Verify your company profile is published on Workloupe

## Support

For technical support, contact: <EMAIL>
Widget generated on: ${new Date().toLocaleDateString()}
Company: ${widgetConfig.companyName}
`;

        zip.file('README.md', instructions);

        // Generate and download ZIP
        zip.generateAsync({type: 'blob'}).then(function(content) {
            const url = window.URL.createObjectURL(content);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${companySlug}-careers-widget.zip`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            alert('{% trans "Widget package downloaded! Extract and follow the README instructions for integration." %}');
        }).catch(function(error) {
            console.error('Error creating ZIP file:', error);
            downloadWidgetFile(widgetCode, companySlug);
        });
    } else {
        downloadWidgetFile(widgetCode, companySlug);
    }
}

function downloadWidgetFile(widgetCode, companySlug) {
    const blob = new Blob([widgetCode], { type: 'text/html' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${companySlug}-careers-widget.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    alert('{% trans "Widget file downloaded! Copy the code and paste it into your website." %}');
}
</script>
{% endblock %}
