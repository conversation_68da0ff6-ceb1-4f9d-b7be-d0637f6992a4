{% extends 'main.html' %}
{% load static %}
{% load i18n %}

{% block content %}
{% csrf_token %}
<div class="careers-page-setup">
    <div class="container py-5">
        <div class="careers-header text-center mb-5">
            <h1 class="display-4 font-weight-bold text-dark mb-3">{% trans "Career Page Setup" %}</h1>
            <p class="lead text-muted">{% trans "Choose the integration method that best suits your needs" %}</p>
            <div class="header-divider mx-auto"></div>
        </div>

        <div class="integration-options row g-4">
            <!-- Option 1: RSS Feed -->
            <div class="col-lg-6 col-xl-4">
                <div class="option-card h-80" onclick="showRSSModal()">
                    <div class="option-icon">
                        <i class="fas fa-rss"></i>
                    </div>
                    <div class="option-content">
                        <h3>{% trans "RSS Feed Integration" %}</h3>
                        <p>{% trans "Already have your own careers page? Get our RSS feed to sync job listings" %}</p>
                        <div class="option-features">
                            <span class="feature-tag">{% trans "Quick Setup" %}</span>
                            <span class="feature-tag">{% trans "Auto Sync" %}</span>
                        </div>
                    </div>
                    <button class="btn-careers-primary">
                        <i class="fas fa-rss me-2"></i>
                        {% trans "Get RSS Feed" %}
                    </button>
                </div>
            </div>

            <!-- Option 2: Full HTML Page -->
            <div class="col-lg-6 col-xl-4">
                <div class="option-card h-80 featured" onclick="window.location.href='{% url 'create_careers_page_full' %}'">
                    <div class="featured-badge">{% trans "Recommended" %}</div>
                    <div class="option-icon">
                        <i class="fas fa-file-code"></i>
                    </div>
                    <div class="option-content">
                        <h3>{% trans "Full HTML Page" %}</h3>
                        <p>{% trans "Let us manage your entire careers page with our professional template" %}</p>
                        <div class="option-features">
                            <span class="feature-tag">{% trans "Professional" %}</span>
                            <span class="feature-tag">{% trans "Customizable" %}</span>
                        </div>
                    </div>
                    <button class="btn-careers-primary">
                        <i class="fas fa-file-code me-2"></i>
                        {% trans "Create Page" %}
                    </button>
                </div>
            </div>

            <!-- Option 3: Custom HTML Widget -->
            <div class="col-lg-6 col-xl-4">
                <div class="option-card h-80" onclick="window.location.href='{% url 'create_careers_widget' %}'">
                    <div class="option-icon">
                        <i class="fas fa-puzzle-piece"></i>
                    </div>
                    <div class="option-content">
                        <h3>{% trans "Custom HTML Widget" %}</h3>
                        <p>{% trans "Perfect for drag & drop website builders like Wix" %}</p>
                        <div class="option-features">
                            <span class="feature-tag">{% trans "Embed Code" %}</span>
                            <span class="feature-tag">{% trans "Flexible" %}</span>
                        </div>
                    </div>
                    <button class="btn-careers-primary">
                        <i class="fas fa-puzzle-piece me-2"></i>
                        {% trans "Get Widget Code" %}
                    </button>
                </div>
            </div>

            <!-- Option 4: WordPress Plugin -->
            <div class="col-lg-6 col-xl-4">
                <div class="option-card h-80" onclick="window.location.href='{% url 'wordpress_integration' %}'">
                    <div class="option-icon">
                        <i class="fab fa-wordpress"></i>
                    </div>
                    <div class="option-content">
                        <h3>{% trans "WordPress Plugin" %}</h3>
                        <p>{% trans "Seamless integration with your WordPress site" %}</p>
                        <div class="option-features">
                            <span class="feature-tag">{% trans "WordPress" %}</span>
                            <span class="feature-tag">{% trans "Easy Install" %}</span>
                        </div>
                    </div>
                    <button class="btn-careers-primary">
                        <i class="fab fa-wordpress me-2"></i>
                        {% trans "Setup WordPress" %}
                    </button>
                </div>
            </div>

            <!-- Option 5: Workloupe Platform -->
            <div class="col-lg-6 col-xl-4">
                <div class="option-card h-80" onclick="window.location.href='{% url 'workloupe_platform' %}'">
                    <div class="option-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <div class="option-content">
                        <h3>{% trans "Workloupe Platform" %}</h3>
                        <p>{% trans "Use our platform as your company's career page" %}</p>
                        <div class="option-features">
                            <span class="feature-tag">{% trans "Hosted" %}</span>
                            <span class="feature-tag">{% trans "Full Featured" %}</span>
                        </div>
                    </div>
                    <button class="btn-careers-primary">
                        <i class="fas fa-globe me-2"></i>
                        {% trans "Setup Platform" %}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- RSS Feed Modal -->
<div class="modal fade" id="rssModal" tabindex="-1" aria-labelledby="rssModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rssModalLabel">
                    <i class="fas fa-rss me-2"></i>
                    {% trans "Your RSS Feed URL" %}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% trans "Use this RSS feed URL to automatically sync your job listings with your existing careers page." %}
                </div>
                <div class="rss-url-container">
                    <label for="rssUrl" class="form-label">{% trans "RSS Feed URL" %}</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="rssUrl" value="https://workloupe.com/{{ current_employer_name }}/jobs.rss" readonly>
                        <button class="btn btn-outline-secondary" type="button" onclick="copyRSSUrl()">
                            <i class="fas fa-copy me-1"></i>
                            {% trans "Copy" %}
                        </button>
                    </div>
                </div>
                <div class="mt-3">
                    <h6>{% trans "Integration Instructions:" %}</h6>
                    <ol class="small text-muted">
                        <li>{% trans "Copy the RSS feed URL above" %}</li>
                        <li>{% trans "Add it to your website's RSS feed reader or job board integration" %}</li>
                        <li>{% trans "Your job listings will automatically sync" %}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.careers-page-setup {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
}

.careers-header {
    position: relative;
}

.header-divider {
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #343a40, #6c757d);
    border-radius: 2px;
    margin-top: 1rem;
}

.integration-options {
    max-width: 1200px;
    margin: 0 auto;
}

.option-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: 
    margin: 8px;
}

.option-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: #343a40;
}

.option-card.featured {
    border-color: #343a40;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.featured-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: #343a40;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.option-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, #343a40, #6c757d);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
}

.option-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #343a40;
    margin-bottom: 1rem;
}

.option-content p {
    color: #6c757d;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.option-features {
    margin-bottom: 2rem;
}

.feature-tag {
    display: inline-block;
    background: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    margin: 0.25rem;
}

.btn-careers-primary {
    background: linear-gradient(135deg, #343a40, #495057);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    width: 100%;
}

.btn-careers-primary:hover {
    background: linear-gradient(135deg, #495057, #6c757d);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.rss-url-container {
    margin-top: 1rem;
}

@media (max-width: 768px) {
    .option-card {
        padding: 1.5rem;
    }

    .option-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .option-content h3 {
        font-size: 1.25rem;
    }
}
</style>

<script>
    function showRSSModal() {
        const modal = new bootstrap.Modal(document.getElementById('rssModal'));
        modal.show();
    }

    function copyRSSUrl() {
        const urlInput = document.getElementById('rssUrl');
        urlInput.select();
        urlInput.setSelectionRange(0, 99999); // For mobile devices

        try {
            document.execCommand('copy');
            // Show success feedback
            const button = event.target.closest('button');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check me-1"></i>{% trans "Copied!" %}';
            button.classList.add('btn-success');
            button.classList.remove('btn-outline-secondary');

            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.remove('btn-success');
                button.classList.add('btn-outline-secondary');
            }, 2000);
        } catch (err) {
            console.error('Failed to copy: ', err);
            alert('{% trans "Failed to copy. Please copy manually." %}');
        }
    }
</script>
{% endblock %}
