{% extends 'main.html' %}
{% load static %}
{% load i18n %}

{% block content %}
{% csrf_token %}
<div class="careers-builder-container">
    <div class="container-fluid h-100">
        <div class="row h-100">
            <!-- Left Panel - Customization Controls -->
            <div class="col-lg-4 col-xl-3 builder-sidebar">
                <div class="sidebar-header">
                    <h4><i class="fas fa-file-code me-2"></i>{% trans "Full Page Builder" %}</h4>
                    <p class="text-muted">{% trans "Create a complete careers page" %}</p>
                </div>

                <div class="customization-panels">
                    <!-- Company Information Panel -->
                    <div class="panel-section">
                        <h5 class="panel-title">
                            <i class="fas fa-building me-2"></i>
                            {% trans "Company Information" %}
                        </h5>

                        <div class="form-group mb-3">
                            <label for="companyName" class="form-label">{% trans "Company Name" %}</label>
                            <input type="text" class="form-control" id="companyName" placeholder="{% trans 'Enter company name' %}" value="{{ employer.employer_name|default:'Your Company' }}">
                        </div>

                        <div class="form-group mb-3">
                            <label for="companyTagline" class="form-label">{% trans "Tagline" %}</label>
                            <input type="text" class="form-control" id="companyTagline" placeholder="{% trans 'Enter company tagline' %}" value="Build what matters. Grow your career. Shape the future.">
                        </div>

                        <div class="form-group mb-3">
                            <label for="companyDescription" class="form-label">{% trans "Company Description" %}</label>
                            <textarea class="form-control" id="companyDescription" rows="3" placeholder="{% trans 'Describe your company' %}">{{ employer.employer_description|default:'We are a leading technology company focused on innovation and excellence.' }}</textarea>
                        </div>

                        <div class="form-group mb-3">
                            <label for="logoUpload" class="form-label">{% trans "Company Logo" %}</label>
                            <input type="file" class="form-control" id="logoUpload" accept="image/*">
                            <small class="form-text text-muted">{% trans "Recommended: 300x120px, PNG or JPG" %}</small>
                        </div>

                        <div class="form-group mb-3">
                            <label for="bannerUpload" class="form-label">{% trans "Header Banner" %}</label>
                            <input type="file" class="form-control" id="bannerUpload" accept="image/*">
                            <small class="form-text text-muted">{% trans "Recommended: 1200x400px, JPG or PNG" %}</small>
                        </div>
                    </div>

                    <!-- Design Customization Panel -->
                    <div class="panel-section">
                        <h5 class="panel-title">
                            <i class="fas fa-palette me-2"></i>
                            {% trans "Design & Colors" %}
                        </h5>

                        <div class="form-group mb-3">
                            <label for="primaryColor" class="form-label">{% trans "Primary Color" %}</label>
                            <input type="color" class="form-control form-control-color" id="primaryColor" value="#343a40">
                        </div>

                        <div class="form-group mb-3">
                            <label for="secondaryColor" class="form-label">{% trans "Secondary Color" %}</label>
                            <input type="color" class="form-control form-control-color" id="secondaryColor" value="#6c757d">
                        </div>

                        <div class="form-group mb-3">
                            <label for="backgroundColor" class="form-label">{% trans "Background Color" %}</label>
                            <input type="color" class="form-control form-control-color" id="backgroundColor" value="#f8f9fa">
                        </div>

                        <div class="form-group mb-3">
                            <label for="pageStyle" class="form-label">{% trans "Page Style" %}</label>
                            <select class="form-select" id="pageStyle">
                                <option value="modern">{% trans "Modern" %}</option>
                                <option value="classic">{% trans "Classic" %}</option>
                                <option value="minimal">{% trans "Minimal" %}</option>
                                <option value="corporate">{% trans "Corporate" %}</option>
                            </select>
                        </div>
                    </div>

                    <!-- Company Stats Panel -->
                    <div class="panel-section">
                        <h5 class="panel-title">
                            <i class="fas fa-chart-bar me-2"></i>
                            {% trans "Company Statistics" %}
                        </h5>

                        <div class="row">
                            <div class="col-6">
                                <div class="form-group mb-3">
                                    <label for="teamSize" class="form-label">{% trans "Team Size" %}</label>
                                    <input type="text" class="form-control" id="teamSize" placeholder="250+" value="250+">
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group mb-3">
                                    <label for="countries" class="form-label">{% trans "Countries" %}</label>
                                    <input type="text" class="form-control" id="countries" placeholder="15+" value="15+">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-6">
                                <div class="form-group mb-3">
                                    <label for="satisfaction" class="form-label">{% trans "Satisfaction" %}</label>
                                    <input type="text" class="form-control" id="satisfaction" placeholder="98%" value="98%">
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group mb-3">
                                    <label for="openPositions" class="form-label">{% trans "Open Positions" %}</label>
                                    <input type="text" class="form-control" id="openPositions" placeholder="25+" value="25+">
                                </div>
                            </div>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="showStats" checked>
                            <label class="form-check-label" for="showStats">
                                {% trans "Show Company Statistics" %}
                            </label>
                        </div>
                    </div>

                    <!-- Company Values Panel -->
                    <div class="panel-section">
                        <h5 class="panel-title">
                            <i class="fas fa-heart me-2"></i>
                            {% trans "Company Values" %}
                        </h5>

                        <div class="form-group mb-3">
                            <label for="value1Title" class="form-label">{% trans "Value 1 Title" %}</label>
                            <input type="text" class="form-control" id="value1Title" value="Innovation">
                        </div>
                        <div class="form-group mb-3">
                            <label for="value1Desc" class="form-label">{% trans "Value 1 Description" %}</label>
                            <textarea class="form-control" id="value1Desc" rows="2">We embrace new ideas and approaches to solve complex challenges.</textarea>
                        </div>

                        <div class="form-group mb-3">
                            <label for="value2Title" class="form-label">{% trans "Value 2 Title" %}</label>
                            <input type="text" class="form-control" id="value2Title" value="Collaboration">
                        </div>
                        <div class="form-group mb-3">
                            <label for="value2Desc" class="form-label">{% trans "Value 2 Description" %}</label>
                            <textarea class="form-control" id="value2Desc" rows="2">We believe diverse perspectives create better solutions.</textarea>
                        </div>

                        <div class="form-group mb-3">
                            <label for="value3Title" class="form-label">{% trans "Value 3 Title" %}</label>
                            <input type="text" class="form-control" id="value3Title" value="Excellence">
                        </div>
                        <div class="form-group mb-3">
                            <label for="value3Desc" class="form-label">{% trans "Value 3 Description" %}</label>
                            <textarea class="form-control" id="value3Desc" rows="2">We strive for excellence in everything we do.</textarea>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="showValues" checked>
                            <label class="form-check-label" for="showValues">
                                {% trans "Show Company Values Section" %}
                            </label>
                        </div>
                    </div>

                    <!-- Generate Page Button -->
                    <div class="panel-section">
                        <button class="btn btn-primary btn-lg w-100" onclick="generateFullPage()">
                            <i class="fas fa-download me-2"></i>
                            {% trans "Download Complete Page" %}
                        </button>
                        <small class="form-text text-muted mt-2">{% trans "Downloads a ZIP file with HTML, CSS, and assets" %}</small>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Live Preview -->
            <div class="col-lg-8 col-xl-9 preview-panel">
                <div class="preview-header">
                    <h4><i class="fas fa-eye me-2"></i>{% trans "Live Preview" %}</h4>
                    <div class="preview-controls">
                        <button class="btn btn-outline-secondary btn-sm" onclick="togglePreviewMode('desktop')">
                            <i class="fas fa-desktop"></i>
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="togglePreviewMode('tablet')">
                            <i class="fas fa-tablet-alt"></i>
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="togglePreviewMode('mobile')">
                            <i class="fas fa-mobile-alt"></i>
                        </button>
                    </div>
                </div>

                <div class="preview-container" id="previewContainer">
                    <div class="preview-frame" id="previewFrame">
                        <!-- Full page preview will be rendered here -->
                        <div id="pagePreview" class="page-preview">
                            <!-- This will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.careers-builder-container {
    height: 100vh;
    overflow: hidden;
}

.builder-sidebar {
    background: #f8f9fa;
    border-right: 1px solid #dee2e6;
    height: 100vh;
    overflow-y: auto;
    padding: 1.5rem;
}

.sidebar-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.panel-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.panel-title {
    color: #343a40;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.preview-panel {
    background: #ffffff;
    height: 100vh;
    overflow: hidden;
    padding: 1.5rem;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.preview-controls .btn {
    margin-left: 0.5rem;
}

.preview-container {
    height: calc(100vh - 120px);
    background: #f8f9fa;
    border-radius: 8px;
    padding: 2rem;
    overflow: auto;
}

.preview-frame {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    min-height: 600px;
    transition: all 0.3s ease;
}

.preview-frame.mobile {
    max-width: 375px;
    margin: 0 auto;
}

.preview-frame.tablet {
    max-width: 768px;
    margin: 0 auto;
}

.page-preview {
    width: 100%;
    height: 100%;
}

.form-control-color {
    width: 100%;
    height: 38px;
}

@media (max-width: 992px) {
    .careers-builder-container {
        height: auto;
    }

    .builder-sidebar,
    .preview-panel {
        height: auto;
    }

    .preview-container {
        height: 500px;
    }
}
</style>

<script>
// Simple Full Page Builder - Working Version
let config = {
    companyName: '{{ employer.employer_name|default:"Your Company"|escapejs }}',
    tagline: 'Build what matters. Grow your career. Shape the future.',
    description: '{{ employer.employer_description|default:"We are a leading technology company focused on innovation and excellence."|escapejs }}',
    primaryColor: '#007bff',
    backgroundColor: '#ffffff'
};

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    updatePreview();

    // Add event listeners to all form inputs
    document.getElementById('companyName').addEventListener('input', updatePreview);
    document.getElementById('companyTagline').addEventListener('input', updatePreview);
    document.getElementById('companyDescription').addEventListener('input', updatePreview);
    document.getElementById('primaryColor').addEventListener('change', updatePreview);
    document.getElementById('backgroundColor').addEventListener('change', updatePreview);
});

function updatePreview() {
    // Get current form values
    const companyName = document.getElementById('companyName').value || 'Your Company';
    const tagline = document.getElementById('companyTagline').value || 'Join our amazing team';
    const description = document.getElementById('companyDescription').value || 'We are building the future.';
    const primaryColor = document.getElementById('primaryColor').value || '#007bff';
    const backgroundColor = document.getElementById('backgroundColor').value || '#ffffff';

    // Generate simple HTML preview
    const previewHTML = `
        <div style="background: ${backgroundColor}; min-height: 100vh; font-family: Arial, sans-serif;">
            <!-- Header -->
            <header style="background: ${primaryColor}; color: white; padding: 60px 20px; text-align: center;">
                <h1 style="margin: 0; font-size: 3rem; font-weight: bold;">${companyName}</h1>
                <p style="margin: 20px 0 0 0; font-size: 1.2rem; opacity: 0.9;">${tagline}</p>
            </header>

            <!-- About Section -->
            <section style="padding: 80px 20px; max-width: 1200px; margin: 0 auto;">
                <div style="text-align: center; margin-bottom: 60px;">
                    <h2 style="color: ${primaryColor}; font-size: 2.5rem; margin-bottom: 20px;">About Us</h2>
                    <p style="font-size: 1.1rem; line-height: 1.6; color: #666; max-width: 800px; margin: 0 auto;">${description}</p>
                </div>
            </section>

            <!-- Jobs Section -->
            <section style="background: #f8f9fa; padding: 80px 20px;">
                <div style="max-width: 1200px; margin: 0 auto; text-align: center;">
                    <h2 style="color: ${primaryColor}; font-size: 2.5rem; margin-bottom: 40px;">Open Positions</h2>
                    <div style="display: grid; gap: 20px; max-width: 800px; margin: 0 auto;">
                        {% for vacancy in active_vacancies %}
                        <div style="background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: left;">
                            <h3 style="color: ${primaryColor}; margin: 0 0 10px 0;">{{ vacancy.vacancy_title }}</h3>
                            <p style="color: #666; margin: 0 0 10px 0;">📍 {{ vacancy.vacancy_city }}, {{ vacancy.vacancy_country }}</p>
                            <p style="color: #666; margin: 0 0 20px 0;">{{ vacancy.vacancy_job_description|truncatechars:100 }}</p>
                            <button style="background: ${primaryColor}; color: white; border: none; padding: 12px 24px; border-radius: 5px; cursor: pointer;">Apply Now</button>
                        </div>
                        {% empty %}
                        <div style="background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                            <h3 style="color: ${primaryColor}; margin: 0 0 10px 0;">Software Engineer</h3>
                            <p style="color: #666; margin: 0 0 10px 0;">📍 Remote</p>
                            <p style="color: #666; margin: 0 0 20px 0;">Join our team and build amazing products...</p>
                            <button style="background: ${primaryColor}; color: white; border: none; padding: 12px 24px; border-radius: 5px; cursor: pointer;">Apply Now</button>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </section>
        </div>
    `;

    // Update the preview
    document.getElementById('pagePreview').innerHTML = previewHTML;
}

function downloadPage() {
    // Get current form values
    const companyName = document.getElementById('companyName').value || 'Your Company';
    const tagline = document.getElementById('companyTagline').value || 'Join our amazing team';
    const description = document.getElementById('companyDescription').value || 'We are building the future.';
    const primaryColor = document.getElementById('primaryColor').value || '#007bff';
    const backgroundColor = document.getElementById('backgroundColor').value || '#ffffff';

    // Generate complete HTML file
    const fullHTML = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${companyName} - Careers</title>
    <style>
        body { margin: 0; padding: 0; font-family: Arial, sans-serif; }
        .header { background: ${primaryColor}; color: white; padding: 60px 20px; text-align: center; }
        .header h1 { margin: 0; font-size: 3rem; font-weight: bold; }
        .header p { margin: 20px 0 0 0; font-size: 1.2rem; opacity: 0.9; }
        .about { padding: 80px 20px; max-width: 1200px; margin: 0 auto; text-align: center; }
        .about h2 { color: ${primaryColor}; font-size: 2.5rem; margin-bottom: 20px; }
        .about p { font-size: 1.1rem; line-height: 1.6; color: #666; max-width: 800px; margin: 0 auto; }
        .jobs { background: #f8f9fa; padding: 80px 20px; }
        .jobs-container { max-width: 1200px; margin: 0 auto; text-align: center; }
        .jobs h2 { color: ${primaryColor}; font-size: 2.5rem; margin-bottom: 40px; }
        .job-card { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: left; margin-bottom: 20px; }
        .job-card h3 { color: ${primaryColor}; margin: 0 0 10px 0; }
        .job-card p { color: #666; margin: 0 0 10px 0; }
        .apply-btn { background: ${primaryColor}; color: white; border: none; padding: 12px 24px; border-radius: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <div class="header">
        <h1>${companyName}</h1>
        <p>${tagline}</p>
    </div>

    <div class="about">
        <h2>About Us</h2>
        <p>${description}</p>
    </div>

    <div class="jobs">
        <div class="jobs-container">
            <h2>Open Positions</h2>
            <div class="job-card">
                <h3>Software Engineer</h3>
                <p>📍 Remote</p>
                <p>Join our team and build amazing products that impact millions of users worldwide.</p>
                <button class="apply-btn">Apply Now</button>
            </div>
        </div>
    </div>
</body>
</html>`;

    // Download the file
    const blob = new Blob([fullHTML], { type: 'text/html' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = companyName.replace(/[^a-z0-9]/gi, '_').toLowerCase() + '_careers.html';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    alert('Career page downloaded successfully!');
}







// Initialize the page when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    updatePreview();

    // Add event listeners for form inputs
    document.getElementById('companyName').addEventListener('input', updatePreview);
    document.getElementById('companyTagline').addEventListener('input', updatePreview);
    document.getElementById('companyDescription').addEventListener('input', updatePreview);
    document.getElementById('primaryColor').addEventListener('change', updatePreview);
    document.getElementById('backgroundColor').addEventListener('change', updatePreview);

    // Add event listener for download button
    const downloadBtn = document.querySelector('button[onclick="generateFullPage()"]');
    if (downloadBtn) {
        downloadBtn.onclick = downloadPage;
    }
});
</script>

{% endblock %}